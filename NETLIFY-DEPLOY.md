# 🚀 Guida al Deploy su Netlify - Smorfia Dreams PWA

## 📋 Checklist Pre-Deploy

### ✅ Preparazione Completata
- [x] Funzione Netlify configurata (`netlify/functions/interpret-dream.js`)
- [x] Endpoint API dinamico configurato (`js/ai.js`)
- [x] File di configurazione Netlify (`netlify.toml`)
- [x] Redirects per SPA (`_redirects`)
- [x] Manifest PWA aggiornato (`manifest.json`)

### 🔧 Configurazione Richiesta

#### 1. Variabili d'Ambiente su Netlify
Dopo aver creato il sito su Netlify, configura questa variabile d'ambiente:

```
GEMINI_API_KEY = la_tua_api_key_di_google_gemini
```

**Come configurare:**
1. Vai su Netlify Dashboard
2. Seleziona il tuo sito
3. Vai su "Site settings" > "Environment variables"
4. Aggiungi: `GEMINI_API_KEY` con il valore della tua API key

#### 2. Impostazioni Build su Netlify
- **Build command:** `echo 'Build completato'`
- **Publish directory:** `.` (root del progetto)
- **Functions directory:** `netlify/functions`

## 🚀 Processo di Deploy

### Opzione 1: Deploy da Git Repository
1. Pusha il codice su GitHub/GitLab
2. Connetti il repository a Netlify
3. Configura le variabili d'ambiente
4. Deploy automatico!

### Opzione 2: Deploy Manuale
1. Comprimi tutti i file del progetto in un ZIP
2. Vai su Netlify Dashboard
3. Trascina il file ZIP nell'area "Deploy"
4. Configura le variabili d'ambiente

## 🔍 Verifica Post-Deploy

### Test Funzionalità
1. **PWA:** Verifica che l'app sia installabile
2. **API:** Testa l'interpretazione dei sogni
3. **Offline:** Controlla che funzioni senza connessione
4. **Mobile:** Testa su dispositivi mobili

### URL di Test
- **Produzione:** `https://your-site-name.netlify.app`
- **Funzione API:** `https://your-site-name.netlify.app/.netlify/functions/interpret-dream`

## 🐛 Troubleshooting

### Errori Comuni

#### "Function not found"
- Verifica che `netlify/functions/interpret-dream.js` esista
- Controlla che `netlify.toml` sia configurato correttamente

#### "GEMINI_API_KEY not configured"
- Verifica che la variabile d'ambiente sia impostata su Netlify
- Controlla che il nome sia esatto: `GEMINI_API_KEY`

#### "CORS errors"
- La funzione Netlify include già gli headers CORS
- Se persistono, verifica che l'endpoint sia corretto

#### PWA non installabile
- Verifica che tutte le icone in `assets/` esistano
- Controlla che il Service Worker sia registrato correttamente

## 📱 Ottimizzazioni Post-Deploy

### Performance
- Le risorse statiche hanno cache a lungo termine
- Il Service Worker gestisce la cache offline
- Le funzioni Netlify sono ottimizzate per velocità

### SEO e PWA
- Meta tags configurati per social sharing
- Manifest PWA completo
- Service Worker per funzionalità offline

### Sicurezza
- Headers di sicurezza configurati
- CORS gestito correttamente
- API key nascosta lato server

## 🔄 Aggiornamenti Futuri

Per aggiornare l'app:
1. Modifica il codice localmente
2. Pusha su Git (se collegato) o ricarica manualmente
3. Netlify rebuilda automaticamente
4. Gli utenti riceveranno l'aggiornamento PWA

## 📞 Supporto

Se incontri problemi:
1. Controlla i logs su Netlify Dashboard
2. Verifica la console del browser per errori JavaScript
3. Testa la funzione API direttamente

---

**Nota:** Questo progetto è configurato per funzionare sia in sviluppo locale che in produzione su Netlify senza modifiche al codice.
