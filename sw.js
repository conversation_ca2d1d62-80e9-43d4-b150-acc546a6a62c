/* SERVICE WORKER - Gestione cache e funzionalità offline */

const CACHE_NAME = 'smorfia-dreams-v1';
const STATIC_CACHE = 'smorfia-static-v1';

const CACHE_FILES = [
  '/',
  '/index.html',
  '/manifest.json',
  '/css/base.css',
  '/css/layout.css',
  '/css/components.css',
  '/css/responsive.css',
  '/js/app.js',
  '/js/audio.js',
  '/js/ai.js',
  '/js/smorfia.js',
  '/js/storage.js',
  '/data/smorfia.json'
];

self.addEventListener('install', (event) => {
  console.log('🔧 Service Worker: Install');
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => cache.addAll(CACHE_FILES))
      .then(() => self.skipWaiting())
      .catch((error) => console.error('❌ Error caching files:', error))
  );
});

self.addEventListener('activate', (event) => {
  console.log('🚀 Service Worker: Activate');
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== STATIC_CACHE && cacheName !== CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => self.clients.claim())
  );
});

self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        if (response) {
          return response;
        }
        return fetch(event.request);
      })
      .catch(() => {
        if (event.request.destination === 'document') {
          return caches.match('/index.html');
        }
      })
  );
});