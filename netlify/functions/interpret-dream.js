exports.handler = async function(event, context) {
  // Headers CORS per tutte le risposte
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Content-Type': 'application/json'
  };

  // Gestione preflight OPTIONS
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  // Verifica il metodo HTTP
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Metodo non consentito' })
    };
  }

  try {
    const requestBody = JSON.parse(event.body);

    // Endpoint Gemini
    const geminiEndpoint = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-002:generateContent';
    const apiKey = process.env.GEMINI_API_KEY;

    if (!apiKey) {
      console.error('GEMINI_API_KEY non configurata');
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ error: 'Configurazione server non valida' })
      };
    }

    // Chiamata a Gemini API usando fetch nativo (disponibile in Node.js 18+)
    const response = await fetch(`${geminiEndpoint}?key=${apiKey}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      console.error('Errore Gemini API:', response.status, response.statusText);
      return {
        statusCode: response.status,
        headers,
        body: JSON.stringify({ error: 'Errore API Gemini' })
      };
    }

    const data = await response.json();

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify(data)
    };
  } catch (error) {
    console.error('Errore funzione Netlify:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Servizio temporaneamente non disponibile' })
    };
  }
};