const fetch = require('node-fetch');

exports.handler = async function(event, context) {
  // Verifica il metodo HTTP
  if (event.httpMethod !== 'POST') {
    return { statusCode: 405, body: JSON.stringify({ error: 'Metodo non consentito' }) };
  }

  try {
    const requestBody = JSON.parse(event.body);
    
    // Endpoint Gemini
    const geminiEndpoint = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-002:generateContent';
    const apiKey = process.env.GEMINI_API_KEY;
    
    // Chiamata a Gemini API
    const response = await fetch(`${geminiEndpoint}?key=${apiKey}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestBody)
    });
    
    const data = await response.json();
    
    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    };
  } catch (error) {
    console.error('Errore:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Servizio temporaneamente non disponibile' })
    };
  }
};