{"name": "smorfia-dreams-proxy", "version": "1.0.0", "description": "Proxy server sicuro per Smorfia Dreams PWA", "main": "proxy-server.js", "scripts": {"start": "node proxy-server.js", "dev": "nodemon proxy-server.js", "test": "echo \"No tests specified\" && exit 0"}, "keywords": ["smorfia", "dreams", "pwa", "proxy", "gemini", "ai"], "author": "Smorfia Dreams Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.0.0", "express-rate-limit": "^6.8.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}