/* AI.JS - Servizio per interpretazione sogni con Google Gemini */

export class AIService {
  constructor() {
    // Determina l'endpoint in base all'ambiente
    this.proxyEndpoint = this.getApiEndpoint();
    this.fallbackMode = false; // Se true, genera numeri casuali
    this.smorfiaDatabase = null; // Cache del database smorfia
  }

  /**
   * Determina l'endpoint API corretto in base all'ambiente
   */
  getApiEndpoint() {
    // In produzione su Netlify
    if (window.location.hostname.includes('netlify.app') ||
        window.location.hostname.includes('netlify.com') ||
        window.location.protocol === 'https:') {
      return '/.netlify/functions/interpret-dream';
    }

    // In sviluppo locale
    return 'http://localhost:3001/api/interpret-dream';
  }

  /**
   * Imposta il database della smorfia per l'AI
   */
  setSmorfiaDatabase(smorfiaData) {
    this.smorfiaDatabase = smorfiaData;
    console.log('✅ Database smorfia caricato nell\'AI Service');
  }

  /**
   * Interpreta un sogno usando Google Gemini AI
   */
  async interpretDream(dreamText) {
    if (!dreamText || dreamText.trim().length < 10) {
      throw new Error('Il testo del sogno è troppo breve');
    }

    const prompt = this.createSmorfiaPrompt(dreamText);

    try {
      const response = await this.callGeminiAPI(prompt);
      return this.parseAIResponse(response);
    } catch (error) {
      console.error('❌ Errore interpretazione AI:', error);
      throw new Error('Servizio di interpretazione temporaneamente non disponibile');
    }
  }

  /**
   * Crea il prompt specifico per la smorfia napoletana
   */
  createSmorfiaPrompt(dreamText) {
    // Crea una stringa con tutti i significati della smorfia
    let smorfiaReference = '';
    if (this.smorfiaDatabase) {
      smorfiaReference = '\n\nDATABASE SMORFIA NAPOLETANA COMPLETO:\n';
      for (const [numero, data] of Object.entries(this.smorfiaDatabase)) {
        smorfiaReference += `${numero} = ${data.significato} (${data.simboli.join(', ')})\n`;
      }
      smorfiaReference += '\nUSA ESCLUSIVAMENTE QUESTI SIGNIFICATI UFFICIALI!\n';
    }

    return `Sei un esperto interprete di sogni e della smorfia napoletana.
Analizza questo sogno: "${dreamText}"
${smorfiaReference}
IMPORTANTE:
1. Devi usare ESCLUSIVAMENTE i significati del database smorfia fornito sopra
2. NON inventare significati diversi da quelli elencati
3. Nell'interpretazione, devi SPIEGARE chiaramente perché scegli ogni numero, collegandolo ai simboli del sogno
4. Cita sempre il significato ESATTO dal database (es: "3 = La gatta", "70 = Il palazzo")

Rispondi in formato JSON:
{
  "interpretazione": "Spiegazione dettagliata che INCLUDE i numeri scelti nel testo con i loro significati ESATTI dal database. Esempio: 'Il numero 3 (La gatta) emerge dal simbolo di astuzia nel sogno, mentre il 70 (Il palazzo) riflette la grandezza architettonica vista...'",
  "simboli": ["simbolo1", "simbolo2", "simbolo3", "simbolo4", "simbolo5"],
  "numeri_suggeriti": [numero1, numero2, numero3, numero4, numero5],
  "spiegazione_numeri": "Breve riassunto del perché questi 5 numeri sono stati scelti per questo sogno specifico, citando i significati esatti dal database"
}

REGOLE FERREE:
- I numeri devono essere tra 1 e 90 secondo la smorfia napoletana
- USA SOLO i significati del database fornito, MAI inventare significati diversi
- Nell'interpretazione, menziona OGNI numero scelto con il suo significato ESATTO dal database
- Collega logicamente i simboli del sogno ai numeri tradizionali del database`;
  }

  /**
   * Chiama l'API di Google Gemini tramite proxy server sicuro
   */
  async callGeminiAPI(prompt) {
    const requestBody = {
      contents: [{
        parts: [{
          text: prompt
        }]
      }]
    };

    try {
      const response = await fetch(this.proxyEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        // Se il proxy non è disponibile, attiva modalità fallback
        if (response.status === 404 || !response.status) {
          console.warn('⚠️ Proxy server non disponibile, modalità fallback attiva');
          return this.generateFallbackResponse(prompt);
        }
        throw new Error(`Proxy Error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.warn('⚠️ Errore connessione proxy, modalità fallback attiva');
      return this.generateFallbackResponse(prompt);
    }
  }
  /**
   * Elabora la risposta dell'AI e estrae i dati strutturati
   */
  parseAIResponse(response) {
    try {
      const content = response.candidates[0].content.parts[0].text;

      // Cerca il JSON nella risposta
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('Formato risposta non valido');
      }

      const parsedData = JSON.parse(jsonMatch[0]);

      // Valida e correggi i numeri suggeriti
      const validatedNumbers = this.validateAndCorrectNumbers(parsedData.numeri_suggeriti || []);

      // Valida i dati
      const result = {
        interpretazione: parsedData.interpretazione || 'Interpretazione non disponibile',
        simboli: Array.isArray(parsedData.simboli) ? parsedData.simboli : [],
        numeri_suggeriti: validatedNumbers,
        spiegazione_numeri: parsedData.spiegazione_numeri || 'Numeri basati sui simboli tradizionali della smorfia',
        significati_numeri: this.getCorrectMeanings(validatedNumbers)
      };

      // Log per debug
      console.log('🔍 Numeri validati:', validatedNumbers);
      console.log('📚 Significati corretti:', result.significati_numeri);

      return result;
    } catch (error) {
      console.error('❌ Errore parsing risposta AI:', error);
      throw new Error('Errore nell\'elaborazione della risposta AI');
    }
  }

  /**
   * Valida e corregge i numeri suggeriti dall'AI
   */
  validateAndCorrectNumbers(aiNumbers) {
    if (!Array.isArray(aiNumbers)) return [];

    // Filtra numeri validi (1-90)
    const validNumbers = aiNumbers.filter(n => n >= 1 && n <= 90);

    // Se abbiamo meno di 5 numeri validi, aggiungi numeri casuali dalla smorfia
    while (validNumbers.length < 5) {
      const randomNumber = Math.floor(Math.random() * 90) + 1;
      if (!validNumbers.includes(randomNumber)) {
        validNumbers.push(randomNumber);
      }
    }

    return validNumbers.slice(0, 5);
  }

  /**
   * Ottiene i significati corretti dal database locale
   */
  getCorrectMeanings(numbers) {
    const meanings = {};
    if (this.smorfiaDatabase) {
      numbers.forEach(numero => {
        const numeroStr = numero.toString();
        if (this.smorfiaDatabase[numeroStr]) {
          meanings[numero] = this.smorfiaDatabase[numeroStr].significato;
        }
      });
    }
    return meanings;
  }

  /**
   * Genera una risposta di fallback quando l'API non è disponibile
   */
  generateFallbackResponse(prompt) {
    const dreamText = prompt.toLowerCase();
    const keywords = {
      'mare': { symbols: ['acqua', 'mare', 'blu'], numbers: [8, 76, 81] },
      'volare': { symbols: ['volo', 'libertà', 'cielo'], numbers: [35, 72, 75] },
      'casa': { symbols: ['dimora', 'famiglia', 'sicurezza'], numbers: [31, 70, 52] },
      'morte': { symbols: ['fine', 'paura', 'cambiamento'], numbers: [47, 48, 90] },
      'amore': { symbols: ['cuore', 'passione', 'felicità'], numbers: [21, 28, 81] },
      'soldi': { symbols: ['ricchezza', 'fortuna', 'successo'], numbers: [46, 77, 82] },
      'cane': { symbols: ['fedeltà', 'amicizia', 'protezione'], numbers: [3, 12, 13] },
      'fuoco': { symbols: ['passione', 'energia', 'pericolo'], numbers: [18, 38, 83] },
      'donna': { symbols: ['femminilità', 'bellezza', 'madre'], numbers: [21, 52, 78] },
      'paura': { symbols: ['terrore', 'ansia', 'spavento'], numbers: [17, 90, 39] }
    };

    let matchedNumbers = [];
    let matchedSymbols = [];

    for (const [key, data] of Object.entries(keywords)) {
      if (dreamText.includes(key)) {
        matchedSymbols.push(...data.symbols);
        matchedNumbers.push(...data.numbers);
      }
    }

    if (matchedNumbers.length === 0) {
      matchedNumbers = [7, 13, 22, 57, 77]; // Numeri fortunati tradizionali
      matchedSymbols = ['mistero', 'fortuna', 'cambiamento'];
    }

    const uniqueNumbers = [...new Set(matchedNumbers)].slice(0, 5);
    while (uniqueNumbers.length < 5) {
      const randomNum = Math.floor(Math.random() * 90) + 1;
      if (!uniqueNumbers.includes(randomNum)) {
        uniqueNumbers.push(randomNum);
      }
    }

    const interpretations = [
      `Il tuo sogno rivela un momento di transizione nella tua vita. Il numero ${uniqueNumbers[0]} emerge dai simboli di cambiamento, mentre il ${uniqueNumbers[1]} riflette la forza interiore necessaria per affrontare le sfide. I numeri ${uniqueNumbers[2]}, ${uniqueNumbers[3]} e ${uniqueNumbers[4]} completano il quadro rappresentando rispettivamente l'equilibrio, la protezione e la fortuna che ti accompagneranno.`,

      `Questo sogno indica una fase di crescita personale. Il numero ${uniqueNumbers[0]} simboleggia il rinnovamento, il ${uniqueNumbers[1]} rappresenta la saggezza acquisita. Gli altri numeri (${uniqueNumbers[2]}, ${uniqueNumbers[3]}, ${uniqueNumbers[4]}) emergono dai simboli di trasformazione, coraggio e nuove opportunità presenti nel sogno.`,

      `Il sogno riflette le tue emozioni interiori. Il numero ${uniqueNumbers[0]} nasce dall'energia emotiva del sogno, il ${uniqueNumbers[1]} dalla ricerca di equilibrio. I numeri ${uniqueNumbers[2]}, ${uniqueNumbers[3]} e ${uniqueNumbers[4]} rappresentano rispettivamente l'intuizione, la protezione spirituale e il successo futuro.`
    ];

    const randomInterpretation = interpretations[Math.floor(Math.random() * interpretations.length)];

    return {
      candidates: [{
        content: {
          parts: [{
            text: JSON.stringify({
              interpretazione: randomInterpretation,
              simboli: matchedSymbols.slice(0, 5),
              numeri_suggeriti: uniqueNumbers,
              spiegazione_numeri: `Questi numeri sono stati scelti analizzando i simboli principali del sogno secondo la tradizione della smorfia napoletana.`,
              significati_numeri: {
                [uniqueNumbers[0]]: "Numero di forza e determinazione",
                [uniqueNumbers[1]]: "Simbolo di cambiamento e rinnovamento"
              }
            })
          }]
        }
      }]
    };
  }
}